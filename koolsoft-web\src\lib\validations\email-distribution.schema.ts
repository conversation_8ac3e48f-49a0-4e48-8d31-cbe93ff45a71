import { z } from 'zod';

/**
 * Email Distribution List Schemas
 */
export const createEmailDistributionListSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name must be less than 255 characters'),
  description: z.string().optional(),
  emails: z.array(z.string().email('Invalid email address')).min(1, 'At least one email is required'),
});

export const updateEmailDistributionListSchema = createEmailDistributionListSchema.partial();

export const listEmailDistributionListsSchema = z.object({
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(20),
  search: z.string().optional(),
  isActive: z.boolean().optional(),
});

/**
 * Report Email Configuration Schemas
 */
export const createReportEmailConfigSchema = z.object({
  reportType: z.enum(['AMC', 'WARRANTY', 'SERVICE', 'SALES', 'CUSTOMER']),
  name: z.string().min(1, 'Name is required').max(255, 'Name must be less than 255 characters'),
  description: z.string().optional(),
  emailSubject: z.string().min(1, 'Email subject is required').max(255, 'Subject must be less than 255 characters'),
  emailBody: z.string().min(1, 'Email body is required'),
  emailTemplateId: z.string().uuid().optional(),
  distributionListId: z.string().uuid().optional(),
  individualRecipients: z.array(z.string().email('Invalid email address')).default([]),
  includeAttachment: z.boolean().default(true),
  attachmentFormat: z.enum(['PDF', 'EXCEL', 'CSV']).default('PDF'),
}).refine(
  (data) => data.distributionListId || data.individualRecipients.length > 0,
  {
    message: 'Either distribution list or individual recipients must be provided',
    path: ['recipients'],
  }
);

export const updateReportEmailConfigSchema = createReportEmailConfigSchema.partial();

export const listReportEmailConfigsSchema = z.object({
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(20),
  reportType: z.enum(['AMC', 'WARRANTY', 'SERVICE', 'SALES', 'CUSTOMER']).optional(),
  search: z.string().optional(),
  isActive: z.boolean().optional(),
});

/**
 * Email Distribution Schemas
 */
export const sendReportEmailSchema = z.object({
  configId: z.string().uuid().optional(),
  reportType: z.enum(['AMC', 'WARRANTY', 'SERVICE', 'SALES', 'CUSTOMER']),
  reportParameters: z.record(z.any()),
  recipients: z.array(z.string().email('Invalid email address')).optional(),
  emailSubject: z.string().optional(),
  emailBody: z.string().optional(),
  includeAttachment: z.boolean().default(true),
  attachmentFormat: z.enum(['PDF', 'EXCEL', 'CSV']).default('PDF'),
}).refine(
  (data) => data.configId || (data.recipients && data.recipients.length > 0),
  {
    message: 'Either config ID or recipients must be provided',
    path: ['recipients'],
  }
);

export const scheduleReportEmailSchema = z.object({
  configId: z.string().uuid(),
  reportParameters: z.record(z.any()),
  scheduledFor: z.string().datetime().optional(), // ISO datetime string
});

/**
 * Email Delivery Tracking Schemas
 */
export const listReportEmailDeliveriesSchema = z.object({
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(20),
  configId: z.string().uuid().optional(),
  reportType: z.enum(['AMC', 'WARRANTY', 'SERVICE', 'SALES', 'CUSTOMER']).optional(),
  status: z.enum(['PENDING', 'SENT', 'FAILED', 'PARTIAL']).optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
});

export const retryEmailDeliverySchema = z.object({
  deliveryId: z.string().uuid(),
  recipients: z.array(z.string().email('Invalid email address')).optional(), // Specific recipients to retry
});

/**
 * Email Template Management Schemas
 */
export const createEmailTemplateSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name must be less than 255 characters'),
  subject: z.string().min(1, 'Subject is required').max(255, 'Subject must be less than 255 characters'),
  bodyHtml: z.string().min(1, 'HTML body is required'),
  bodyText: z.string().min(1, 'Text body is required'),
  description: z.string().optional(),
  variables: z.array(z.string()).default([]),
  category: z.string().optional(),
});

export const updateEmailTemplateSchema = createEmailTemplateSchema.partial();

export const listEmailTemplatesSchema = z.object({
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(20),
  category: z.string().optional(),
  search: z.string().optional(),
  isActive: z.boolean().optional(),
});

/**
 * Type exports
 */
export type CreateEmailDistributionListInput = z.infer<typeof createEmailDistributionListSchema>;
export type UpdateEmailDistributionListInput = z.infer<typeof updateEmailDistributionListSchema>;
export type ListEmailDistributionListsInput = z.infer<typeof listEmailDistributionListsSchema>;

export type CreateReportEmailConfigInput = z.infer<typeof createReportEmailConfigSchema>;
export type UpdateReportEmailConfigInput = z.infer<typeof updateReportEmailConfigSchema>;
export type ListReportEmailConfigsInput = z.infer<typeof listReportEmailConfigsSchema>;

export type SendReportEmailInput = z.infer<typeof sendReportEmailSchema>;
export type ScheduleReportEmailInput = z.infer<typeof scheduleReportEmailSchema>;

export type ListReportEmailDeliveriesInput = z.infer<typeof listReportEmailDeliveriesSchema>;
export type RetryEmailDeliveryInput = z.infer<typeof retryEmailDeliverySchema>;

export type CreateEmailTemplateInput = z.infer<typeof createEmailTemplateSchema>;
export type UpdateEmailTemplateInput = z.infer<typeof updateEmailTemplateSchema>;
export type ListEmailTemplatesInput = z.infer<typeof listEmailTemplatesSchema>;
