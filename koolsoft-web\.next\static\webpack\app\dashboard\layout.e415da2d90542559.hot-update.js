"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/layout/dashboard-layout.tsx":
/*!****************************************************!*\
  !*** ./src/components/layout/dashboard-layout.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardLayout: () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _base_layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./base-layout */ \"(app-pages-browser)/./src/components/layout/base-layout.tsx\");\n/* harmony import */ var _page_header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./page-header */ \"(app-pages-browser)/./src/components/layout/page-header.tsx\");\n/* harmony import */ var _lib_hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/hooks/useAuth */ \"(app-pages-browser)/./src/lib/hooks/useAuth.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cog.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column-increasing.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/kanban.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRightLeft,BarChart3,BarChart4,Bell,Calendar,ChevronLeft,ChevronRight,Clock,Cog,CreditCard,Database,Eye,FileText,Kanban,LayoutDashboard,LogOut,Plus,Settings,Shield,User,Users,Wrench,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* __next_internal_client_entry_do_not_use__ DashboardLayout auto */ \nvar _s = $RefreshSig$();\nvar _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n/**\n * DashboardLayout Component\n *\n * A layout with a collapsible sidebar navigation for the dashboard and related pages.\n */ function DashboardLayout(param) {\n    let { children, title, actions, breadcrumbs, showAdminLink = false, requireAuth = true, allowedRoles = [] } = param;\n    _s();\n    _s1();\n    const { user, hasRole, logout } = (0,_lib_hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Navigation items for the sidebar\n    const navItems = [\n        {\n            name: 'Dashboard',\n            href: '/dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 44,\n                columnNumber: 11\n            }, this)\n        },\n        {\n            name: 'Customers',\n            href: '/customers',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 48,\n                columnNumber: 11\n            }, this)\n        },\n        {\n            name: 'AMC Management',\n            href: '/amc',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 52,\n                columnNumber: 11\n            }, this),\n            children: [\n                {\n                    name: 'All Contracts',\n                    href: '/amc',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'Payments',\n                    href: '/amc/payments',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'Service Dates',\n                    href: '/amc/service-dates',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'New Contract',\n                    href: '/amc/new',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 13\n                    }, this),\n                    roles: [\n                        'ADMIN',\n                        'MANAGER',\n                        'EXECUTIVE'\n                    ]\n                }\n            ]\n        },\n        {\n            name: 'Warranty Management',\n            href: '/warranties',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 74,\n                columnNumber: 11\n            }, this),\n            children: [\n                {\n                    name: 'Overview',\n                    href: '/warranties',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'In-Warranty',\n                    href: '/warranties/in-warranty',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'Out-of-Warranty',\n                    href: '/warranties/out-warranty',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'Components',\n                    href: '/warranties/components',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'Status Dashboard',\n                    href: '/warranties/status',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'Alerts',\n                    href: '/warranties/alerts',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'BLUESTAR',\n                    href: '/warranties/bluestar',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 13\n                    }, this)\n                }\n            ]\n        },\n        {\n            name: 'History Cards',\n            href: '/history-cards',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 107,\n                columnNumber: 11\n            }, this),\n            children: [\n                {\n                    name: 'All Cards',\n                    href: '/history-cards',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'New Card',\n                    href: '/history-cards/new',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 13\n                    }, this),\n                    roles: [\n                        'ADMIN',\n                        'MANAGER',\n                        'EXECUTIVE'\n                    ]\n                }\n            ]\n        },\n        {\n            name: 'Reference Data',\n            href: '/reference-data',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 121,\n                columnNumber: 11\n            }, this),\n            roles: [\n                'ADMIN',\n                'MANAGER'\n            ]\n        },\n        {\n            name: 'Service Management',\n            href: '/service',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 126,\n                columnNumber: 11\n            }, this),\n            children: [\n                {\n                    name: 'Overview',\n                    href: '/service',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'New Service Report',\n                    href: '/service/new',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 13\n                    }, this),\n                    roles: [\n                        'ADMIN',\n                        'MANAGER',\n                        'EXECUTIVE'\n                    ]\n                },\n                {\n                    name: 'Service History',\n                    href: '/service/history',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'Service Dashboard',\n                    href: '/service/dashboard',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'Service Scheduling',\n                    href: '/service/scheduling',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 13\n                    }, this),\n                    roles: [\n                        'ADMIN',\n                        'MANAGER',\n                        'EXECUTIVE'\n                    ]\n                }\n            ]\n        },\n        {\n            name: 'Sales',\n            href: '/sales',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 153,\n                columnNumber: 11\n            }, this),\n            children: [\n                {\n                    name: 'Sales Dashboard',\n                    href: '/sales/dashboard',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'Lead Management',\n                    href: '/leads',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'Sales Pipeline',\n                    href: '/sales/pipeline',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'Quotations',\n                    href: '/quotations',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 13\n                    }, this)\n                }\n            ]\n        },\n        {\n            name: 'Reports',\n            href: '/reports',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 174,\n                columnNumber: 11\n            }, this),\n            roles: [\n                'ADMIN',\n                'MANAGER',\n                'EXECUTIVE'\n            ],\n            children: [\n                {\n                    name: 'All Reports',\n                    href: '/reports',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'Crystal Reports',\n                    href: '/reports/crystal',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'Report Viewer',\n                    href: '/reports/viewer',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'Conversion Reports',\n                    href: '/conversions/reports',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'Scheduled Reports',\n                    href: '/reports/schedules',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 13\n                    }, this),\n                    roles: [\n                        'ADMIN',\n                        'MANAGER'\n                    ]\n                },\n                {\n                    name: 'Formula Engine',\n                    href: '/reports/formulas',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Calculator, {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 13\n                    }, this),\n                    roles: [\n                        'ADMIN',\n                        'MANAGER'\n                    ]\n                }\n            ]\n        },\n        {\n            name: 'Notifications',\n            href: '/notifications',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 206,\n                columnNumber: 11\n            }, this),\n            children: [\n                {\n                    name: 'Preferences',\n                    href: '/notifications/preferences',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 13\n                    }, this)\n                },\n                {\n                    name: 'History',\n                    href: '/notifications/history',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 13\n                    }, this)\n                }\n            ]\n        },\n        {\n            name: 'Admin',\n            href: '/admin',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 219,\n                columnNumber: 11\n            }, this),\n            roles: [\n                'ADMIN'\n            ],\n            children: [\n                {\n                    name: 'Notifications',\n                    href: '/admin/notifications',\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 13\n                    }, this),\n                    roles: [\n                        'ADMIN',\n                        'MANAGER'\n                    ]\n                }\n            ]\n        }\n    ];\n    // Handle sign out\n    const handleSignOut = async ()=>{\n        await logout();\n        router.push('/auth/login');\n    };\n    // Toggle expanded state for menu items with children\n    const toggleExpanded = (href)=>{\n        setExpandedItems((prev)=>prev.includes(href) ? prev.filter((item)=>item !== href) : [\n                ...prev,\n                href\n            ]);\n    };\n    // Check if a nav item is active\n    const isActive = (href, children)=>{\n        if (pathname === href) return true;\n        if (children) {\n            return children.some((child)=>pathname === child.href || pathname.startsWith(\"\".concat(child.href, \"/\")));\n        }\n        return pathname.startsWith(\"\".concat(href, \"/\"));\n    };\n    // Check if a nav item should be expanded (active or manually expanded)\n    const isExpanded = (href, children)=>{\n        if (!children) return false;\n        return expandedItems.includes(href) || children.some((child)=>pathname === child.href || pathname.startsWith(\"\".concat(child.href, \"/\")));\n    };\n    // Default profile actions if none provided\n    const defaultActions = actions || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                asChild: true,\n                variant: \"outline\",\n                size: \"sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/profile\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                            className: \"h-4 w-4 mr-1\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this),\n                        \"My Profile\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                variant: \"destructive\",\n                size: \"sm\",\n                onClick: handleSignOut,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                        className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this),\n                    \"Sign Out\"\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n        lineNumber: 256,\n        columnNumber: 37\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_base_layout__WEBPACK_IMPORTED_MODULE_4__.BaseLayout, {\n        requireAuth: requireAuth,\n        allowedRoles: allowedRoles,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen bg-gray-50 overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"bg-white h-full shadow-md transition-all duration-300 flex flex-col\", sidebarCollapsed ? \"w-16\" : \"w-64\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 flex items-center justify-between border-b\",\n                            children: [\n                                !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-gray-800\",\n                                    children: \"KoolSoft\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 35\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    size: \"icon\",\n                                    onClick: ()=>setSidebarCollapsed(!sidebarCollapsed),\n                                    \"aria-label\": sidebarCollapsed ? \"Expand Sidebar\" : \"Collapse Sidebar\",\n                                    className: \"border border-gray-200 bg-white shadow-sm\",\n                                    children: sidebarCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 35\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 74\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"space-y-1 px-2\",\n                                children: navItems.map((item)=>{\n                                    // Skip items that require specific roles if user doesn't have them\n                                    if (item.roles && !item.roles.some((role)=>hasRole([\n                                            role\n                                        ]))) {\n                                        return null;\n                                    }\n                                    const hasChildren = item.children && item.children.length > 0;\n                                    const itemIsActive = isActive(item.href, item.children);\n                                    const itemIsExpanded = isExpanded(item.href, item.children);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: [\n                                            hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleExpanded(item.href),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"w-full flex items-center px-3 py-2 rounded-md transition-colors text-left\", itemIsActive ? 'bg-primary text-primary-foreground' : 'text-gray-700 hover:bg-secondary', sidebarCollapsed && \"justify-center\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: sidebarCollapsed ? \"\" : \"mr-2\",\n                                                        children: item.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"flex-1\",\n                                                                children: item.name\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRightLeft_BarChart3_BarChart4_Bell_Calendar_ChevronLeft_ChevronRight_Clock_Cog_CreditCard_Database_Eye_FileText_Kanban_LayoutDashboard_LogOut_Plus_Settings_Shield_User_Users_Wrench_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"h-4 w-4 transition-transform\", itemIsExpanded && \"rotate-90\")\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 36\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: item.href,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"flex items-center px-3 py-2 rounded-md transition-colors\", itemIsActive ? 'bg-primary text-primary-foreground' : 'text-gray-700 hover:bg-secondary', sidebarCollapsed && \"justify-center\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: sidebarCollapsed ? \"\" : \"mr-2\",\n                                                        children: item.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    !sidebarCollapsed && item.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 35\n                                            }, this),\n                                            hasChildren && itemIsExpanded && !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4 space-y-1\",\n                                                children: item.children.map((child)=>{\n                                                    // Skip child items that require specific roles if user doesn't have them\n                                                    if (child.roles && !child.roles.some((role)=>hasRole([\n                                                            role\n                                                        ]))) {\n                                                        return null;\n                                                    }\n                                                    const childIsActive = pathname === child.href || pathname.startsWith(\"\".concat(child.href, \"/\"));\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: child.href,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"flex items-center px-3 py-2 rounded-md transition-colors text-sm\", childIsActive ? 'bg-primary/10 text-primary border-l-2 border-primary' : 'text-gray-600 hover:bg-secondary hover:text-gray-900'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"mr-2\",\n                                                                children: child.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            child.name\n                                                        ]\n                                                    }, child.href, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 28\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 76\n                                            }, this)\n                                        ]\n                                    }, item.href, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 22\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_page_header__WEBPACK_IMPORTED_MODULE_5__.PageHeader, {\n                            title: title,\n                            breadcrumbs: breadcrumbs,\n                            actions: defaultActions,\n                            showDashboardLink: false,\n                            showAdminLink: showAdminLink\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 overflow-y-auto p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n            lineNumber: 269,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\components\\\\layout\\\\dashboard-layout.tsx\",\n        lineNumber: 268,\n        columnNumber: 10\n    }, this);\n}\n_s(DashboardLayout, \"WLuNwL4OzhILL3andIlcBk4oTT8=\", false, function() {\n    return [\n        _lib_hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c1 = DashboardLayout;\n_s1(DashboardLayout, \"NQatqSJhSsNuM5FwsqKkPkSw3bs=\", false, function() {\n    return [\n        _lib_hooks_useAuth__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = DashboardLayout;\nvar _c;\n$RefreshReg$(_c, \"DashboardLayout\");\nvar _c1;\n$RefreshReg$(_c1, \"DashboardLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/dashboard-layout.tsx\n"));

/***/ })

});